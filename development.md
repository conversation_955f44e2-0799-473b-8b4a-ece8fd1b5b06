# WarrantyAI Development Guide

## 🛠 Tech Stack Details

### Frontend Framework
- **Next.js v15+**: App Router, Server Components, Image Optimization
- **React 18+**: Concurrent features, Suspense, Error Boundaries
- **TypeScript**: Type safety (optional for MVP)

### Styling & Design
- **Tailwind CSS v4+**: Utility-first CSS framework
- **Custom CSS Variables**: For dynamic theming
- **Responsive Design**: Mobile-first approach

### Animation Libraries
- **GSAP**: High-performance animations, ScrollTrigger
- **Framer Motion**: React-specific animations
- **Three.js**: 3D graphics and WebGL
- **React Three Fiber**: React renderer for Three.js
- **React Three Drei**: Useful helpers for R3F

### UI Components
- **Lucide React**: Modern icon library
- **Custom Components**: Built from scratch for brand consistency
- **Headless UI**: Accessible component primitives

### Backend Simulation
- **JSON Files**: Static data for demo purposes
- **localStorage**: Client-side data persistence
- **Session Storage**: Temporary data storage
- **Cookies**: User preferences

## 📱 Page Structure & Sections

### HomePage (`/`)
#### Hero Section
- **Layout**: Split-screen with 3D visualization
- **Animation**: Parallax scrolling, floating elements
- **CTA**: Primary action button with hover effects
- **Demo**: Mini warranty tracking simulation

#### Problem Section
- **Layout**: Grid with animated icons
- **Content**: Pain points with statistics
- **Animation**: Scroll-triggered reveals

#### Solution Section
- **Layout**: Feature cards with 3D previews
- **Animation**: Morphing shapes, hover effects
- **Interactive**: Clickable feature demos

#### Features Section
- **Layout**: Carousel with 3D product models
- **Animation**: Auto-rotating showcase
- **Content**: Core MVP features

#### Pricing Section
- **Layout**: Equal-height cards
- **Animation**: Hover transformations
- **Interactive**: Plan comparison

#### Trust Section
- **Layout**: Testimonial carousel
- **Animation**: Smooth transitions
- **Content**: Social proof elements

### DemoPage (`/demo`)
#### Demo Interface
- **Layout**: Dashboard simulation
- **Functionality**: Working warranty tracker
- **Data**: Realistic product examples
- **Interactions**: Upload, scan, manage

#### Feature Showcase
- **Layout**: Multi-level demo scenarios
- **Animation**: Step-by-step walkthroughs
- **Content**: Real-world use cases

### Additional Pages
- **About** (`/about`): Company story, team
- **Pricing** (`/pricing`): Detailed plans
- **Contact** (`/contact`): Support information

## 🎨 Design System

### Color Palette
```css
/* Primary Colors */
--primary-50: #eff6ff
--primary-500: #3b82f6
--primary-900: #1e3a8a

/* Accent Colors */
--accent-cyan: #06b6d4
--accent-purple: #8b5cf6
--accent-green: #10b981

/* Neutral Colors */
--gray-50: #f9fafb
--gray-900: #111827

/* Dark Mode */
--dark-bg: #0f172a
--dark-surface: #1e293b
```

### Typography
```css
/* Font Families */
--font-primary: 'Inter', sans-serif
--font-display: 'Space Grotesk', sans-serif
--font-mono: 'JetBrains Mono', monospace

/* Font Sizes */
--text-xs: 0.75rem
--text-sm: 0.875rem
--text-base: 1rem
--text-lg: 1.125rem
--text-xl: 1.25rem
--text-2xl: 1.5rem
--text-3xl: 1.875rem
--text-4xl: 2.25rem
--text-5xl: 3rem
```

### Spacing System
```css
/* Spacing Scale */
--space-1: 0.25rem
--space-2: 0.5rem
--space-4: 1rem
--space-8: 2rem
--space-16: 4rem
--space-32: 8rem
```

## 🎭 Animation & Effects System

### GSAP Animations
- **ScrollTrigger**: Parallax backgrounds, reveal animations
- **Timeline**: Complex sequence animations
- **Morphing**: Shape transformations
- **Physics**: Realistic motion effects

### Three.js 3D Elements
- **Product Models**: Warranty item visualizations
- **Particle Systems**: Background effects
- **Interactive Objects**: Clickable 3D elements
- **Camera Controls**: Smooth navigation

### Custom Effects Pool
1. **Multi-User Cursor Simulation**
2. **Matrix Rain Effect**
3. **AI Eye Tracker Animation**
4. **Parallax Scroll Backgrounds**
5. **Scroll-Synced Animations**
6. **3D Tilt on Hover**
7. **Typing Text Effect**
8. **Terminal Simulation**
9. **Morphing Shapes**
10. **Floating Tooltips**

## 📊 Data Structure

### Warranty Item Schema
```json
{
  "id": "uuid",
  "name": "Product Name",
  "brand": "Brand Name",
  "category": "electronics|home|vehicle|appliance",
  "purchaseDate": "2024-01-15",
  "warrantyPeriod": 24,
  "warrantyExpiry": "2026-01-15",
  "serialNumber": "ABC123456",
  "purchasePrice": 299.99,
  "retailer": "Best Buy",
  "status": "active|expiring|expired",
  "images": ["receipt.jpg", "product.jpg"],
  "documents": ["warranty.pdf"],
  "reminders": [
    {
      "type": "warranty_expiry",
      "date": "2025-12-15",
      "sent": false
    }
  ]
}
```

### User Profile Schema
```json
{
  "id": "uuid",
  "name": "John Doe",
  "email": "<EMAIL>",
  "plan": "free|pro|business",
  "preferences": {
    "notifications": true,
    "theme": "light|dark|auto",
    "language": "en"
  },
  "stats": {
    "totalItems": 15,
    "activeWarranties": 12,
    "expiringSoon": 2,
    "totalValue": 4599.99
  }
}
```

## 🔧 Component Architecture

### Core Components
- **Layout**: Header, Footer, Navigation
- **Hero**: Animated hero sections
- **FeatureCard**: Interactive feature displays
- **ProductModel**: 3D product visualizations
- **WarrantyCard**: Item display cards
- **Dashboard**: Main interface components
- **Modal**: Overlay dialogs
- **Form**: Input components with validation

### Animation Components
- **ParallaxSection**: Scroll-based animations
- **RevealOnScroll**: Intersection observer animations
- **TypewriterText**: Typing effect text
- **FloatingElements**: Animated background elements
- **ThreeDModel**: Three.js model wrapper

## 🚀 Performance Optimization

### Image Optimization
- **Next.js Image**: Automatic optimization
- **WebP Format**: Modern image formats
- **Lazy Loading**: Intersection observer
- **Responsive Images**: Multiple sizes

### Code Splitting
- **Dynamic Imports**: Route-based splitting
- **Component Lazy Loading**: On-demand loading
- **Bundle Analysis**: Size monitoring

### Animation Performance
- **GPU Acceleration**: CSS transforms
- **RequestAnimationFrame**: Smooth animations
- **Intersection Observer**: Efficient scroll detection
- **Debounced Events**: Optimized event handling

## 📱 Responsive Design Strategy

### Breakpoints
```css
/* Mobile First */
--mobile: 320px
--tablet: 768px
--desktop: 1024px
--wide: 1440px
```

### Layout Patterns
- **Mobile**: Single column, stacked elements
- **Tablet**: Two-column grid, condensed navigation
- **Desktop**: Multi-column layouts, full navigation
- **Wide**: Centered content, expanded spacing

## 🔒 Security & Privacy

### Data Protection
- **Local Storage**: Encrypted sensitive data
- **No External APIs**: Frontend-only MVP
- **Privacy First**: No tracking, no analytics
- **GDPR Compliant**: User data control

### Content Security
- **CSP Headers**: Content Security Policy
- **XSS Protection**: Input sanitization
- **HTTPS Only**: Secure connections
- **Safe Assets**: Verified external resources
